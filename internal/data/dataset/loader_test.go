package dataset

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

func TestLoaderBasic(t *testing.T) {
	// Create a temporary CSV file
	csvContent := `name,age,salary,active
John,25,50000.5,true
<PERSON>,30,75000.0,false
<PERSON>,35,60000.25,true`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Load CSV data
	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Verify basic structure
	if data.NumRows != 3 {
		t.<PERSON><PERSON>("Expected 3 rows, got %d", data.NumRows)
	}

	if data.NumColumns != 4 {
		t.Errorf("Expected 4 columns, got %d", data.NumColumns)
	}

	expectedHeaders := []string{"name", "age", "salary", "active"}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.Errorf("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Verify some data content
	if len(data.Records) != 3 {
		t.Errorf("Expected 3 records, got %d", len(data.Records))
	}
	if data.Records[0][0] != "John" {
		t.Errorf("Expected first record name to be 'John', got %s", data.Records[0][0])
	}
}

func TestLoaderFileNotFound(t *testing.T) {
	loader := NewLoader()
	data := loader.LoadCSV("nonexistent.csv")

	// Should return nil for nonexistent file
	if data != nil {
		t.Fatal("Expected nil data for nonexistent file, got valid data")
	}
}

func TestLoaderEmptyFile(t *testing.T) {
	// Create an empty CSV file
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "empty.csv")

	err := os.WriteFile(csvFile, []byte(""), 0644)
	if err != nil {
		t.Fatalf("Failed to create empty CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)

	// Should return data with zero values for empty CSV
	if data == nil {
		t.Fatal("Expected data to be returned for empty CSV")
	}
	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}

func TestLoaderHeaderOnly(t *testing.T) {
	// Create a CSV file with only headers
	csvContent := `name,age,salary,active`
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "header_only.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create header-only CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Should have headers but no data rows
	if data.NumColumns != 4 {
		t.Errorf("Expected 4 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 data rows, got %d", data.NumRows)
	}
	if len(data.Headers) != 4 {
		t.Errorf("Expected 4 headers, got %d", len(data.Headers))
	}
	if len(data.Records) != 0 {
		t.Errorf("Expected 0 records, got %d", len(data.Records))
	}
}

// TestLoadCSVToDataset tests the integrated CSV loading with type conversion
func TestLoadCSVToDataset(t *testing.T) {
	// Create test CSV content with mixed types
	csvContent := `name,age,salary,active,department
John,25,50000.5,true,Engineering
Jane,30,75000.0,false,Marketing
Bob,35,60000.25,true,Engineering`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Define feature types for conversion
	featureTypes := map[string]features.FeatureType{
		"name":       features.StringFeature,
		"age":        features.IntegerFeature,
		"salary":     features.FloatFeature,
		"department": features.StringFeature,
	}

	// Load CSV to dataset with string target
	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "active")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify dataset structure
	if dataset.totalSize != 3 {
		t.Errorf("Expected dataset size 3, got %d", dataset.totalSize)
	}

	// Verify feature order
	expectedFeatures := []string{"name", "age", "salary", "department"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify columns were created correctly
	nameCol, err := dataset.GetColumn("name")
	if err != nil {
		t.Errorf("Failed to get name column: %v", err)
	}
	if nameCol.GetType() != features.StringFeature {
		t.Errorf("Expected StringFeature for name, got %v", nameCol.GetType())
	}

	ageCol, err := dataset.GetColumn("age")
	if err != nil {
		t.Errorf("Failed to get age column: %v", err)
	}
	if ageCol.GetType() != features.IntegerFeature {
		t.Errorf("Expected IntegerFeature for age, got %v", ageCol.GetType())
	}

	salaryCol, err := dataset.GetColumn("salary")
	if err != nil {
		t.Errorf("Failed to get salary column: %v", err)
	}
	if salaryCol.GetType() != features.FloatFeature {
		t.Errorf("Expected FloatFeature for salary, got %v", salaryCol.GetType())
	}

	// Verify data values
	nameValue, err := nameCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get name value: %v", err)
	}
	if nameValue != "John" {
		t.Errorf("Expected name 'John', got %v", nameValue)
	}

	ageValue, err := ageCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get age value: %v", err)
	}
	if ageValue != int64(25) {
		t.Errorf("Expected age 25, got %v", ageValue)
	}

	salaryValue, err := salaryCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get salary value: %v", err)
	}
	if salaryValue != 50000.5 {
		t.Errorf("Expected salary 50000.5, got %v", salaryValue)
	}

	// Verify target values
	target0, err := dataset.GetTarget(0)
	if err != nil {
		t.Errorf("Failed to get target 0: %v", err)
	}
	if target0 != "true" {
		t.Errorf("Expected target 'true', got %v", target0)
	}

	target1, err := dataset.GetTarget(1)
	if err != nil {
		t.Errorf("Failed to get target 1: %v", err)
	}
	if target1 != "false" {
		t.Errorf("Expected target 'false', got %v", target1)
	}

	// Verify feature metadata
	nameInfo, err := dataset.GetFeatureInfo("name")
	if err != nil {
		t.Errorf("Failed to get name feature info: %v", err)
	}
	if nameInfo.Type != features.StringFeature {
		t.Errorf("Expected StringFeature in metadata, got %v", nameInfo.Type)
	}
}

// TestLoadCSVToDatasetTargetNotFound tests error handling for missing target column
func TestLoadCSVToDatasetTargetNotFound(t *testing.T) {
	csvContent := `name,age,salary
John,25,50000.5
Jane,30,75000.0`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Try to load with non-existent target column
	_, err = LoadCSVToDataset[string](csvFile, featureTypes, "nonexistent")
	if err == nil {
		t.Error("Expected error for missing target column, got nil")
	}
	if err != nil && err.Error() != "target column 'nonexistent' not found in CSV headers" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

// TestLoadCSVToDatasetFileNotFound tests error handling for missing CSV file
func TestLoadCSVToDatasetFileNotFound(t *testing.T) {
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
	}

	_, err := LoadCSVToDataset[string]("nonexistent.csv", featureTypes, "target")
	if err == nil {
		t.Error("Expected error for missing file, got nil")
	}
}

// TestLoadCSVToDatasetWithNullValues tests handling of missing/null values
func TestLoadCSVToDatasetWithNullValues(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,,75000.0,B
Bob,35,,C
,40,60000.25,D`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_nulls.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Test null handling in age column (row 1 has empty age)
	ageCol, err := dataset.GetColumn("age")
	if err != nil {
		t.Fatalf("Failed to get age column: %v", err)
	}

	// Row 1 should have null age
	_, err = ageCol.GetValue(1)
	if err == nil {
		t.Error("Expected error for null age value, got nil")
	}

	// Test null handling in salary column (row 2 has empty salary)
	salaryCol, err := dataset.GetColumn("salary")
	if err != nil {
		t.Fatalf("Failed to get salary column: %v", err)
	}

	_, err = salaryCol.GetValue(2)
	if err == nil {
		t.Error("Expected error for null salary value, got nil")
	}

	// Test null handling in name column (row 3 has empty name)
	nameCol, err := dataset.GetColumn("name")
	if err != nil {
		t.Fatalf("Failed to get name column: %v", err)
	}

	_, err = nameCol.GetValue(3)
	if err == nil {
		t.Error("Expected error for null name value, got nil")
	}

	// Verify valid values still work
	validAge, err := ageCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get valid age: %v", err)
	}
	if validAge != int64(25) {
		t.Errorf("Expected age 25, got %v", validAge)
	}
}

// TestLoadCSVToDatasetMissingFeatureType tests handling of missing feature type metadata
func TestLoadCSVToDatasetMissingFeatureType(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,30,75000.0,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Missing feature type for 'salary' column
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
		// "salary" is missing - should default to StringFeature
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify salary column was created as StringFeature (default)
	salaryCol, err := dataset.GetColumn("salary")
	if err != nil {
		t.Fatalf("Failed to get salary column: %v", err)
	}
	if salaryCol.GetType() != features.StringFeature {
		t.Errorf("Expected StringFeature for missing type, got %v", salaryCol.GetType())
	}

	// Verify salary value is stored as string
	salaryValue, err := salaryCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get salary value: %v", err)
	}
	if salaryValue != "50000.5" {
		t.Errorf("Expected salary '50000.5' as string, got %v", salaryValue)
	}
}

// TestLoadCSVToDatasetEmptyDataset tests loading CSV with only headers
func TestLoadCSVToDatasetEmptyDataset(t *testing.T) {
	csvContent := `name,age,salary,target`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "empty_data.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify empty dataset structure
	if dataset.totalSize != 0 {
		t.Errorf("Expected dataset size 0, got %d", dataset.totalSize)
	}

	// Verify columns were still created
	expectedFeatures := []string{"name", "age", "salary"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify columns exist but are empty
	nameCol, err := dataset.GetColumn("name")
	if err != nil {
		t.Errorf("Failed to get name column: %v", err)
	}
	if nameCol.GetSize() != 0 {
		t.Errorf("Expected empty name column, got size %d", nameCol.GetSize())
	}
}

// TestLoadCSVToDatasetSingleRow tests loading CSV with single data row
func TestLoadCSVToDatasetSingleRow(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "single_row.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify single row dataset
	if dataset.totalSize != 1 {
		t.Errorf("Expected dataset size 1, got %d", dataset.totalSize)
	}

	// Verify data values
	nameCol, err := dataset.GetColumn("name")
	if err != nil {
		t.Fatalf("Failed to get name column: %v", err)
	}
	nameValue, err := nameCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get name value: %v", err)
	}
	if nameValue != "John" {
		t.Errorf("Expected name 'John', got %v", nameValue)
	}

	// Verify target
	target, err := dataset.GetTarget(0)
	if err != nil {
		t.Errorf("Failed to get target: %v", err)
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}
}

// TestLoadCSVToDataset_UnsupportedFeatureType tests error handling for unsupported feature types
func TestLoadCSVToDataset_UnsupportedFeatureType(t *testing.T) {
	csvContent := `name,age,target
John,25,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Use an invalid feature type (this would require modifying the enum, so we'll simulate)
	// For now, test with valid types but add a test for the default case
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify dataset was created successfully
	if dataset.totalSize != 1 {
		t.Errorf("Expected dataset size 1, got %d", dataset.totalSize)
	}
}

// TestLoadCSVToDataset_TargetConversionError tests error handling for target conversion failures
func TestLoadCSVToDataset_TargetConversionError(t *testing.T) {
	csvContent := `name,age,target
John,25,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
	}

	// Try to load with int target type (this should work for string targets)
	dataset, err := LoadCSVToDataset[int](csvFile, featureTypes, "target")
	if err == nil {
		t.Error("Expected error for target type conversion, got nil")
	}
	if dataset != nil {
		t.Error("Expected nil dataset on conversion error")
	}
}

// TestLoadCSVToDataset_LargeDataset tests loading a larger dataset
func TestLoadCSVToDataset_LargeDataset(t *testing.T) {
	// Create a larger CSV content
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,30,75000.0,B
Bob,35,60000.25,A
Alice,28,55000.75,B
Charlie,40,80000.0,A
Diana,32,65000.5,B
Eve,27,52000.25,A
Frank,38,72000.0,B
Grace,29,58000.75,A
Henry,33,68000.5,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "large_test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset, err := LoadCSVToDataset[string](csvFile, featureTypes, "target")
	if err != nil {
		t.Fatalf("LoadCSVToDataset failed: %v", err)
	}

	// Verify dataset structure
	if dataset.totalSize != 10 {
		t.Errorf("Expected dataset size 10, got %d", dataset.totalSize)
	}

	// Verify some data values
	nameCol, err := dataset.GetColumn("name")
	if err != nil {
		t.Fatalf("Failed to get name column: %v", err)
	}

	nameValue, err := nameCol.GetValue(0)
	if err != nil {
		t.Errorf("Failed to get name value: %v", err)
	}
	if nameValue != "John" {
		t.Errorf("Expected name 'John', got %v", nameValue)
	}

	// Test target distribution
	view := dataset.CreateView([]int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9})
	dist, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get target distribution: %v", err)
	}

	if dist["A"] != 5 {
		t.Errorf("Expected 5 'A' targets, got %d", dist["A"])
	}
	if dist["B"] != 5 {
		t.Errorf("Expected 5 'B' targets, got %d", dist["B"])
	}
}
