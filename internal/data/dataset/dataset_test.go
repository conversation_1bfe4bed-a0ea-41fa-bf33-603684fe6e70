// Package dataset provides comprehensive testing for Dataset and DatasetView structures.
//
// Test Coverage:
// - dataset_test.go: Core functionality tests for Dataset and DatasetView
// - column_test.go: Column type implementations and interface compliance
// - loader_test.go: CSV data loading and parsing
//
// Test Data:
// Uses small synthetic datasets with known values for predictable assertions.
// No external files required - all test data generated in-memory.
//
// Security: No sensitive data in tests, uses string targets for simplicity.
// Performance: Tests focus on correctness over performance benchmarks.
package dataset

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// TestDatasetAndDatasetView validates core Dataset and DatasetView functionality.
//
// Purpose: Comprehensive integration test covering dataset creation, column addition,
// view creation, feature access, target retrieval, and child view operations.
//
// Test Data: 5 rows with mixed data types:
// - age (int64): [25, 30, 35, 40, 45]
// - salary (float64): [50000.0, 60000.0, 70000.0, 80000.0, 90000.0]
// - department (string): ["Engineering", "Sales", "Marketing", "Engineering", "Sales"]
// - targets (string): ["A", "B", "A", "B", "A"]
//
// Constraints: All test data has no null values for simplicity.
// Relationships: Tests Dataset -> DatasetView -> ChildView hierarchy.
// Side effects: Creates temporary dataset structures, no persistent state.
func TestDatasetAndDatasetView(t *testing.T) {
	// Create a new dataset
	dataset := NewDataset[string](10)

	// Add some test data
	ages := []int64{25, 30, 35, 40, 45}
	ageNulls := []bool{false, false, false, false, false}
	err := dataset.AddIntColumn("age", ages, ageNulls)
	if err != nil {
		t.Fatalf("Failed to add age column: %v", err)
	}

	salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
	salaryNulls := []bool{false, false, false, false, false}
	err = dataset.AddFloatColumn("salary", salaries, salaryNulls)
	if err != nil {
		t.Fatalf("Failed to add salary column: %v", err)
	}

	departments := []string{"Engineering", "Sales", "Marketing", "Engineering", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	err = dataset.AddStringColumn("department", departments, deptNulls)
	if err != nil {
		t.Fatalf("Failed to add department column: %v", err)
	}

	// Add targets
	targets := []string{"A", "B", "A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Test basic dataset functionality
	if dataset.GetRowCount() != 5 {
		t.Errorf("Expected dataset size 5, got %d", dataset.GetRowCount())
	}

	// Test getting columns
	ageCol, err := dataset.GetColumn("age")
	if err != nil {
		t.Fatalf("Failed to get age column: %v", err)
	}
	if ageCol.GetSize() != 5 {
		t.Errorf("Expected age column size 5, got %d", ageCol.GetSize())
	}

	// Test getting a value from column
	val, err := ageCol.GetValue(0)
	if err != nil {
		t.Fatalf("Failed to get value from age column: %v", err)
	}
	if val != int64(25) {
		t.Errorf("Expected age value 25, got %v", val)
	}

	// Test creating a view with subset of indices
	viewIndices := []int{0, 2, 4} // indices 0, 2, 4 (ages 25, 35, 45)
	view := dataset.CreateView(viewIndices)

	if view.GetSize() != 3 {
		t.Errorf("Expected view size 3, got %d", view.GetSize())
	}

	// Test getting feature value from view (logical index 0 = physical index 0)
	ageValue, err := view.GetFeatureValue(0, "age")
	if err != nil {
		t.Fatalf("Failed to get age value from view: %v", err)
	}
	if ageValue != int64(25) {
		t.Errorf("Expected age value 25, got %v", ageValue)
	}

	// Test getting feature value from view (logical index 1 = physical index 2)
	ageValue2, err := view.GetFeatureValue(1, "age")
	if err != nil {
		t.Fatalf("Failed to get age value from view: %v", err)
	}
	if ageValue2 != int64(35) {
		t.Errorf("Expected age value 35, got %v", ageValue2)
	}

	// Test getting target from view
	target, err := view.GetTarget(0)
	if err != nil {
		t.Fatalf("Failed to get target from view: %v", err)
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	// Test target distribution
	dist, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get target distribution: %v", err)
	}
	if dist["A"] != 3 {
		t.Errorf("Expected 3 'A' targets in view, got %d", dist["A"])
	}
	if dist["B"] != 0 {
		t.Errorf("Expected 0 'B' targets in view, got %d", dist["B"])
	}

	// Test creating child view
	childIndices := []int{0, 2} // logical indices in view (physical indices 0, 4)
	childView := view.CreateChildView(childIndices)

	if childView.GetSize() != 2 {
		t.Errorf("Expected child view size 2, got %d", childView.GetSize())
	}

	// Test getting value from child view
	childAge, err := childView.GetFeatureValue(0, "age")
	if err != nil {
		t.Fatalf("Failed to get age from child view: %v", err)
	}
	if childAge != int64(25) {
		t.Errorf("Expected child view age 25, got %v", childAge)
	}

	childAge2, err := childView.GetFeatureValue(1, "age")
	if err != nil {
		t.Fatalf("Failed to get age from child view: %v", err)
	}
	if childAge2 != int64(45) {
		t.Errorf("Expected child view age 45, got %v", childAge2)
	}
}

// TestDatasetViewCaching validates target distribution caching behavior.
//
// Purpose: Ensures GetTargetDistribution() correctly caches results and returns
// identical data on subsequent calls without recalculation.
//
// Test Strategy:
// 1. Create minimal dataset with known target distribution
// 2. Call GetTargetDistribution() twice on same view
// 3. Verify both calls return identical results
// 4. Implicitly tests cache hit path (no direct cache inspection)
//
// Constraints: Uses simple 3-row dataset for predictable distribution.
// Security: No sensitive data, uses string targets.
// Performance: Tests caching efficiency (second call should be O(1)).
// Side effects: Modifies view's internal cache state (targetDistDirty flag).
func TestDatasetViewCaching(t *testing.T) {
	// Create a simple dataset with known target distribution
	dataset := NewDataset[string](3)

	// Add targets: 2 "A"s, 1 "B" for predictable distribution
	targets := []string{"A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Create view covering all rows
	view := dataset.CreateView([]int{0, 1, 2})

	// Get distribution first time (should calculate and cache)
	dist1, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get target distribution: %v", err)
	}

	// Get distribution second time (should return cached result)
	dist2, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get cached target distribution: %v", err)
	}

	// Verify cache returns identical results
	if len(dist1) != len(dist2) {
		t.Errorf("Cached distribution differs from calculated: lengths %d vs %d", len(dist1), len(dist2))
	}
	for k, v := range dist1 {
		if dist2[k] != v {
			t.Errorf("Cached distribution value differs for key %s: %d vs %d", k, v, dist2[k])
		}
	}

	// Verify expected distribution values
	if dist1["A"] != 2 {
		t.Errorf("Expected 2 'A' targets, got %d", dist1["A"])
	}
	if dist1["B"] != 1 {
		t.Errorf("Expected 1 'B' target, got %d", dist1["B"])
	}
}

// TestDataset_GetColumn_ErrorCases tests error cases for GetColumn
func TestDataset_GetColumn_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting non-existent column
	_, err := dataset.GetColumn("nonexistent")
	if err == nil {
		t.Error("Expected error for non-existent column, got nil")
	}
}

// TestDataset_GetFeatureInfo_ErrorCases tests error cases for GetFeatureInfo
func TestDataset_GetFeatureInfo_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting feature info for non-existent feature
	_, err := dataset.GetFeatureInfo("nonexistent")
	if err == nil {
		t.Error("Expected error for non-existent feature info, got nil")
	}
}


// TestDataset_GetTarget_ErrorCases tests error cases for GetTarget
func TestDataset_GetTarget_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting target with invalid index
	_, err := dataset.GetTarget(-1)
	if err == nil {
		t.Error("Expected error for negative index, got nil")
	}

	_, err = dataset.GetTarget(0)
	if err == nil {
		t.Error("Expected error for out of bounds index, got nil")
	}

	// Add some targets and test valid access
	dataset.AddTarget("A")
	dataset.AddTarget("B")

	target, err := dataset.GetTarget(0)
	if err != nil {
		t.Errorf("Failed to get valid target: %v", err)
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	// Test out of bounds after adding targets
	_, err = dataset.GetTarget(5)
	if err == nil {
		t.Error("Expected error for out of bounds index, got nil")
	}
}

// TestDataset_SetFeatureInfo tests SetFeatureInfo method
func TestDataset_SetFeatureInfo(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test setting valid feature info
	featureInfo := features.NewFeatureInfo("test", features.StringFeature, "Test feature")
	dataset.SetFeatureInfo("test", featureInfo)

	// Verify feature info was set
	retrievedInfo, err := dataset.GetFeatureInfo("test")
	if err != nil {
		t.Errorf("Failed to get feature info after setting: %v", err)
	}
	if retrievedInfo.Type != features.StringFeature {
		t.Errorf("Expected StringFeature, got %v", retrievedInfo.Type)
	}

	// Test setting feature info with nil (should not panic)
	dataset.SetFeatureInfo("test_nil", nil)

	// Verify nil was set
	retrievedNil, err := dataset.GetFeatureInfo("test_nil")
	if err != nil {
		t.Errorf("Failed to get nil feature info: %v", err)
	}
	if retrievedNil != nil {
		t.Errorf("Expected nil feature info, got %v", retrievedNil)
	}
}

// TestDatasetView_GetFeatureValue_ErrorCases tests error cases for GetFeatureValue
func TestDatasetView_GetFeatureValue_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Add some data
	data := []int64{1, 2, 3}
	nullMask := []bool{false, false, false}
	err := dataset.AddIntColumn("age", data, nullMask)
	if err != nil {
		t.Fatalf("Failed to add column: %v", err)
	}

	view := dataset.CreateView([]int{0, 1, 2})

	// Test getting value from non-existent feature
	_, err = view.GetFeatureValue(0, "nonexistent")
	if err == nil {
		t.Error("Expected error for non-existent feature, got nil")
	}

	// Test getting value with invalid logical index
	_, err = view.GetFeatureValue(-1, "age")
	if err == nil {
		t.Error("Expected error for negative logical index, got nil")
	}

	_, err = view.GetFeatureValue(5, "age")
	if err == nil {
		t.Error("Expected error for out of bounds logical index, got nil")
	}
}

// TestDatasetView_GetTarget_ErrorCases tests error cases for GetTarget
func TestDatasetView_GetTarget_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)
	dataset.AddTarget("A")
	dataset.AddTarget("B")
	dataset.AddTarget("C")

	view := dataset.CreateView([]int{0, 2}) // Skip index 1

	// Test getting target with invalid logical index
	_, err := view.GetTarget(-1)
	if err == nil {
		t.Error("Expected error for negative logical index, got nil")
	}

	_, err = view.GetTarget(5)
	if err == nil {
		t.Error("Expected error for out of bounds logical index, got nil")
	}

	// Test valid access
	target, err := view.GetTarget(0)
	if err != nil {
		t.Errorf("Failed to get valid target: %v", err)
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	target, err = view.GetTarget(1)
	if err != nil {
		t.Errorf("Failed to get valid target: %v", err)
	}
	if target != "C" {
		t.Errorf("Expected target 'C', got %v", target)
	}
}

// TestDatasetView_GetActiveIndices tests GetActiveIndices method
func TestDatasetView_GetActiveIndices(t *testing.T) {
	dataset := NewDataset[string](10)
	dataset.AddTarget("A")
	dataset.AddTarget("B")
	dataset.AddTarget("C")

	view := dataset.CreateView([]int{0, 2, 1}) // Mixed order

	activeIndices := view.GetActiveIndices()
	expectedIndices := []int{0, 2, 1}

	if len(activeIndices) != len(expectedIndices) {
		t.Errorf("Expected %d active indices, got %d", len(expectedIndices), len(activeIndices))
	}

	for i, expected := range expectedIndices {
		if activeIndices[i] != expected {
			t.Errorf("Expected active index %d at position %d, got %d", expected, i, activeIndices[i])
		}
	}
}
